/**
 * Enhanced Authentication Store - Production Ready
 * Integrates with Azure Functions backend using exact type matching
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { backendApiClient } from '../services/backend-api-client'
import type {
  StandardToken,
  UserContext,
  AuthResult
} from '../types/backend'

export interface AuthState {
  // Core auth state
  user: UserContext | null
  token: StandardToken | null
  refreshToken: string | null
  isAuthenticated: boolean
  expiresAt: number | null

  // UI state
  loading: boolean
  error: string | null
  lastUpdated: string | null

  // Session state
  sessionId: string | null
  deviceInfo: {
    userAgent: string
    browser: string
    os: string
    isMobile: boolean
  } | null

  // Internal state
  _hydrated: boolean
}

export interface AuthActions {
  // Authentication actions
  login: (credentials: { email: string; password: string; rememberMe?: boolean }) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>

  // Profile actions
  updateProfile: (data: Partial<UserContext>) => Promise<void>

  // Token management
  setToken: (tokenData: StandardToken) => void
  clearAuth: () => void

  // Azure AD B2C specific
  handleB2CAuthResult: (userInfo: any, sessionInfo: any) => void
  syncUserWithBackend: (accessToken?: string) => Promise<void>

  // Session management
  checkAuthStatus: () => boolean
  validateSession: () => Promise<boolean>

  // Utility methods
  isTokenExpired: () => boolean
  timeUntilExpiry: () => number
  hasPermission: (permission: string) => boolean
  hasRole: (role: string) => boolean
  isInOrganization: (organizationId: string) => boolean
}

export type AuthStore = AuthState & AuthActions

const getDeviceInfo = () => {
  if (typeof window === 'undefined') return null

  const userAgent = navigator.userAgent
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)

  let browser = 'Unknown'
  if (userAgent.includes('Chrome')) browser = 'Chrome'
  else if (userAgent.includes('Firefox')) browser = 'Firefox'
  else if (userAgent.includes('Safari')) browser = 'Safari'
  else if (userAgent.includes('Edge')) browser = 'Edge'

  let os = 'Unknown'
  if (userAgent.includes('Windows')) os = 'Windows'
  else if (userAgent.includes('Mac')) os = 'macOS'
  else if (userAgent.includes('Linux')) os = 'Linux'
  else if (userAgent.includes('Android')) os = 'Android'
  else if (userAgent.includes('iOS')) os = 'iOS'

  return { userAgent, browser, os, isMobile }
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      expiresAt: null,
      loading: false,
      error: null,
      lastUpdated: null,
      sessionId: null,
      deviceInfo: null,
      _hydrated: false,

      // Authentication actions
      login: async (credentials) => {
        set({ loading: true, error: null })

        try {
          // Login and get token
          const tokenData = await backendApiClient.login(credentials)

          // Set token in API client
          backendApiClient.setToken(tokenData.accessToken)

          // Get user profile
          const user = await backendApiClient.getProfile()

          // Generate session ID
          const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

          set({
            user,
            token: tokenData,
            refreshToken: tokenData.refreshToken,
            isAuthenticated: true,
            expiresAt: tokenData.expiresAt,
            sessionId,
            deviceInfo: getDeviceInfo(),
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })

          // Store refresh token separately for security
          if (typeof window !== 'undefined' && tokenData.refreshToken) {
            localStorage.setItem('refresh_token', tokenData.refreshToken)
          }
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Login failed',
            isAuthenticated: false,
            user: null,
            token: null,
            refreshToken: null,
            expiresAt: null,
            sessionId: null,
          })
          throw error
        }
      },

      logout: async () => {
        set({ loading: true, error: null })

        try {
          // Call backend logout
          await backendApiClient.logout()
        } catch (error) {
          // Continue with logout even if API call fails
          console.warn('Logout API call failed:', error)
        } finally {
          // Clear all auth state
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            expiresAt: null,
            sessionId: null,
            deviceInfo: null,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })

          // Clear API client token
          backendApiClient.clearToken()
        }
      },

      refreshAuth: async () => {
        const { refreshToken } = get()

        // For Azure AD B2C, refresh tokens are not available in browser environments
        // This is a security feature of B2C. Instead, we should handle token expiry
        // by redirecting to login when needed.
        if (!refreshToken) {
          console.warn('[Auth Store] No refresh token available - this is expected for Azure AD B2C')
          throw new Error('No refresh token available')
        }

        set({ loading: true, error: null })

        try {
          // Refresh token through API client
          const refreshResult = await backendApiClient.auth.refreshToken(refreshToken)

          // Get updated user profile
          const user = await backendApiClient.getProfile()

          if (!refreshResult.accessToken) {
            throw new Error('Token refresh failed')
          }

          // Create StandardToken object
          const tokenData: StandardToken = {
            accessToken: refreshResult.accessToken,
            refreshToken: refreshResult.refreshToken || refreshToken,
            userId: user.id,
            email: user.email,
            roles: user.roles || [],
            organizationIds: user.organizationIds || [],
            tenantId: user.tenantId || '',
            expiresAt: refreshResult.expiresAt || (Date.now() + 3600000) // 1 hour default
          }

          set({
            user,
            token: tokenData,
            isAuthenticated: true,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })
        } catch (error: any) {
          console.warn('[Auth Store] Token refresh failed:', error.message)
          set({
            loading: false,
            error: error.message || 'Token refresh failed',
            isAuthenticated: false,
            user: null,
            token: null,
            refreshToken: null,
            expiresAt: null,
            sessionId: null,
          })

          // Clear API client token
          backendApiClient.clearToken()
          throw error
        }
      },

      updateProfile: async (data) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        set({ loading: true, error: null })

        try {
          // Update profile through API client
          const updatedUser = { ...user, ...data, updatedAt: new Date().toISOString() }

          set({
            user: updatedUser,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Profile update failed',
          })
          throw error
        }
      },

      setToken: (tokenData) => {
        backendApiClient.setToken(tokenData.accessToken)

        set({
          token: tokenData,
          refreshToken: tokenData.refreshToken,
          isAuthenticated: true,
          expiresAt: tokenData.expiresAt,
          error: null,
          lastUpdated: new Date().toISOString(),
        })

        // Store refresh token
        if (typeof window !== 'undefined' && tokenData.refreshToken) {
          localStorage.setItem('refresh_token', tokenData.refreshToken)
        }

        // Validate token with backend and sync user data
        get().syncUserWithBackend(tokenData.accessToken)
      },

      // New method to sync user data with backend
      syncUserWithBackend: async (accessToken?: string) => {
        try {
          const token = accessToken || get().token?.accessToken
          if (!token) {
            console.warn('[Auth Store] No token available for backend sync')
            return
          }

          console.log('[Auth Store] Syncing user data with backend...')

          // Set token for API calls
          backendApiClient.setToken(token)

          // Try to get user profile from backend
          const userProfile = await backendApiClient.getProfile()
          if (userProfile) {
            // Update user data from backend
            set({
              user: userProfile,
              lastUpdated: new Date().toISOString(),
            })
            console.log('[Auth Store] User data synced with backend successfully:', userProfile.email)
          } else {
            console.warn('[Auth Store] No user profile returned from backend')
          }
        } catch (error: any) {
          console.warn('[Auth Store] Failed to sync user data with backend:', error.message || error)
          // Don't clear auth on sync failure - user might still be authenticated
          // This is especially important for B2C where backend might be down
        }
      },

      // Method to extract and set B2C token from session
      extractB2CToken: async () => {
        try {
          console.log('[Auth Store] Extracting B2C token from session...')

          const response = await fetch('/api/auth/token', {
            method: 'GET',
            credentials: 'include',
          })

          if (response.ok) {
            const tokenData = await response.json()
            if (tokenData.accessToken && tokenData.userInfo) {
              console.log('[Auth Store] Successfully extracted B2C token')

              // Create StandardToken object
              const standardToken: StandardToken = {
                accessToken: tokenData.accessToken,
                refreshToken: '', // B2C doesn't provide refresh tokens in browser
                userId: tokenData.userInfo.localAccountId,
                email: tokenData.userInfo.username,
                roles: ['user'],
                organizationIds: [],
                tenantId: tokenData.userInfo.tenantId,
                expiresAt: tokenData.expiresOn,
                scope: 'openid profile email',
                tokenType: 'Bearer'
              }

              // Set token in store and API client
              get().setToken(standardToken)

              // Sync with backend
              await get().syncUserWithBackend(tokenData.accessToken)

              return true
            }
          }

          return false
        } catch (error) {
          console.warn('[Auth Store] Failed to extract B2C token:', error)
          return false
        }
      },

      clearAuth: () => {
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          expiresAt: null,
          sessionId: null,
          deviceInfo: null,
          error: null,
          lastUpdated: new Date().toISOString(),
        })

        backendApiClient.clearToken()
      },

      checkAuthStatus: () => {
        const { token, expiresAt } = get()

        if (!token || !expiresAt) {
          set({ isAuthenticated: false })
          return false
        }

        const isValid = Date.now() < expiresAt

        if (!isValid) {
          get().clearAuth()
          return false
        }

        set({ isAuthenticated: true })
        return true
      },

      validateSession: async () => {
        try {
          const authResult = await backendApiClient.validateToken()

          if (authResult.success && authResult.user) {
            set({
              user: authResult.user,
              isAuthenticated: true,
              error: null,
              lastUpdated: new Date().toISOString(),
            })
            return true
          } else {
            get().clearAuth()
            return false
          }
        } catch (error) {
          // Handle network errors gracefully
          const errorMessage = error instanceof Error ? error.message : String(error)
          console.warn('Session validation failed:', errorMessage)

          // Only clear auth if it's not a network error
          if (!errorMessage.includes('Network error') && !errorMessage.includes('NETWORK_ERROR')) {
            get().clearAuth()
          }
          return false
        }
      },

      // Utility methods
      isTokenExpired: () => {
        const { expiresAt } = get()
        if (!expiresAt) return true
        return Date.now() >= expiresAt
      },

      timeUntilExpiry: () => {
        const { expiresAt } = get()
        if (!expiresAt) return 0
        return Math.max(0, expiresAt - Date.now())
      },

      hasPermission: (permission) => {
        const { user } = get()
        return user?.permissions?.includes(permission) || false
      },

      hasRole: (role) => {
        const { user } = get()
        return user?.roles?.includes(role) || false
      },

      isInOrganization: (organizationId) => {
        const { user } = get()
        return user?.organizationIds?.includes(organizationId) ||
               user?.organizationId === organizationId ||
               false
      },

      // Azure AD B2C specific authentication handler
      handleB2CAuthResult: async (userInfo, sessionInfo) => {
        try {
          set({ loading: true, error: null })

          // Create initial user context from B2C user info
          const initialUser: UserContext = {
            id: userInfo.localAccountId || userInfo.username,
            email: userInfo.username,
            name: userInfo.name || userInfo.username,
            tenantId: userInfo.tenantId,
            roles: [],
            organizationIds: [],
            permissions: [],
            lastLoginAt: new Date().toISOString(),
            isEmailVerified: true,
          }

          // Create token object (access token is HTTP-only, so we'll use a placeholder)
          const token: StandardToken = {
            accessToken: 'server_managed', // Actual token is HTTP-only
            refreshToken: '',
            userId: userInfo.localAccountId || userInfo.username,
            email: userInfo.username,
            roles: [],
            organizationIds: [],
            tenantId: userInfo.tenantId,
            expiresAt: sessionInfo.expiresOn || (Date.now() + 3600000),
            scope: sessionInfo.scopes?.join(' ') || 'openid profile email',
            tokenType: 'Bearer'
          }

          // Generate session ID
          const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

          // Set initial state
          set({
            user: initialUser,
            token,
            isAuthenticated: true,
            expiresAt: token.expiresAt,
            sessionId,
            deviceInfo: getDeviceInfo(),
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })

          // Now fetch complete user data from backend
          try {
            const backendUser = await backendApiClient.request('/users/me/profile', {
              method: 'GET'
            })

            if (backendUser) {
              // Update with backend user data
              const enhancedUser: UserContext = {
                ...initialUser,
                ...backendUser,
                id: initialUser.id, // Keep B2C ID
                email: initialUser.email, // Keep B2C email
              }

              set({
                user: enhancedUser,
                lastUpdated: new Date().toISOString(),
              })
            }
          } catch (backendError) {
            console.warn('[Auth Store] Failed to fetch user data from backend:', backendError)
            // Continue with B2C user data only
          }

        } catch (error) {
          console.error('[Auth Store] Failed to handle B2C auth result:', error)
          set({
            error: 'Failed to process authentication result',
            loading: false,
          })
          throw error
        }
      },
    }),
    {
      name: 'auth-store-v2',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        expiresAt: state.expiresAt,
        sessionId: state.sessionId,
        deviceInfo: state.deviceInfo,
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true

          // Check for B2C session cookies first
          if (typeof window !== 'undefined') {
            console.log('[Auth Store] Checking for B2C session cookies...')
            console.log('[Auth Store] All cookies:', document.cookie)

            const getCookie = (name: string): string | null => {
              const cookies = document.cookie.split('; ')
              const cookie = cookies.find(row => row.startsWith(`${name}=`))
              return cookie ? cookie.split('=')[1] : null
            }

            const msalSession = getCookie('msal_session')
            const msalUserInfo = getCookie('msal_user_info')

            console.log('[Auth Store] MSAL session cookie:', msalSession)
            console.log('[Auth Store] MSAL user info cookie:', msalUserInfo)

            if (msalSession && msalUserInfo) {
              try {
                const sessionData = JSON.parse(decodeURIComponent(msalSession))
                const userInfo = JSON.parse(decodeURIComponent(msalUserInfo))

                console.log('[Auth Store] Parsed session data:', sessionData)
                console.log('[Auth Store] Parsed user info:', userInfo)

                if (sessionData.isAuthenticated && sessionData.expiresOn && Date.now() < sessionData.expiresOn) {
                  console.log('[Auth Store] Valid B2C session found, updating state')
                  // Update state with B2C session data
                  state.handleB2CAuthResult(userInfo, sessionData)
                  return
                } else {
                  console.log('[Auth Store] B2C session expired or invalid')
                }
              } catch (error) {
                console.warn('[Auth Store] Failed to parse B2C session cookies:', error)
              }
            } else {
              console.log('[Auth Store] No B2C session cookies found')
            }
          }

          // Fallback to stored token validation
          if (state.token && state.expiresAt) {
            const isValid = Date.now() < state.expiresAt
            if (!isValid) {
              state.clearAuth()
            } else {
              // Set token in API client
              backendApiClient.setToken(state.token?.accessToken || '')
            }
          }
        }
      },
    }
  )
)

// ============================================================================
// SELECTOR HOOKS
// ============================================================================

// Core selectors
export const useUser = () => useAuthStore((state) => state.user)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.loading)
export const useAuthError = () => useAuthStore((state) => state.error)
export const useToken = () => useAuthStore((state) => state.token)

// Action hooks
export const useLogin = () => useAuthStore((state) => state.login)
export const useLogout = () => useAuthStore((state) => state.logout)
export const useUpdateProfile = () => useAuthStore((state) => state.updateProfile)
export const useRefreshAuth = () => useAuthStore((state) => state.refreshAuth)

// Permission hooks
export const usePermissions = () => {
  const user = useUser()
  const hasPermission = useAuthStore((state) => state.hasPermission)
  const hasRole = useAuthStore((state) => state.hasRole)
  const isInOrganization = useAuthStore((state) => state.isInOrganization)

  return {
    permissions: user?.permissions || [],
    roles: user?.roles || [],
    hasPermission,
    hasRole,
    isInOrganization,
  }
}

// Session hooks
export const useSession = () => {
  const {
    isAuthenticated,
    sessionId,
    deviceInfo,
    isTokenExpired,
    timeUntilExpiry,
    validateSession,
    checkAuthStatus
  } = useAuthStore()

  return {
    isAuthenticated,
    sessionId,
    deviceInfo,
    isExpired: isTokenExpired(),
    timeUntilExpiry: timeUntilExpiry(),
    validate: validateSession,
    check: checkAuthStatus,
  }
}

// Organization context hook
export const useOrganizationContext = () => {
  const user = useUser()
  const isInOrganization = useAuthStore((state) => state.isInOrganization)

  return {
    currentOrganization: user?.organizationId || null,
    organizations: user?.organizationIds || [],
    isInOrganization,
    canAccessOrganization: (orgId: string) => isInOrganization(orgId),
  }
}

// Auth status hook - for components that need to wait for auth check
export const useAuthStatus = () => {
  const { isAuthenticated, loading, error, _hydrated } = useAuthStore()

  return {
    isAuthenticated,
    isLoading: loading,
    error,
    isReady: _hydrated && !loading, // Auth check is complete
    needsAuth: _hydrated && !loading && !isAuthenticated,
  }
}

// Session checked hook - for components that need to know if session has been validated
export const useSessionChecked = () => {
  const { _hydrated, loading, isAuthenticated } = useAuthStore()

  return {
    sessionChecked: _hydrated && !loading,
    isAuthenticated,
    isLoading: loading,
  }
}
